{"name": "helloworld", "version": "1.0.0", "license": "AGPL-3.0-or-later", "engines": {"node": "^20.0.0", "npm": "^10.0.0"}, "scripts": {"build": "vite build", "dev": "vite --mode development build", "watch": "vite --mode development build --watch", "lint": "eslint src", "stylelint": "stylelint src/**/*.vue src/**/*.scss src/**/*.css"}, "type": "module", "browserslist": ["extends @nextcloud/browserslist-config"], "dependencies": {"@nextcloud/vue": "^8.27.0", "vue": "^2.7.16"}, "devDependencies": {"@nextcloud/browserslist-config": "^3.0.1", "@nextcloud/eslint-config": "^8.4.2", "@nextcloud/stylelint-config": "^3.1.0", "@nextcloud/vite-config": "^1.5.2", "vite": "^6.3.5"}}