# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2023-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Npm audit fix and compile

on:
  workflow_dispatch:
  schedule:
    # At 2:30 on Sundays
    - cron: '30 2 * * 0'

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        branches: ['main', 'master', 'stable31', 'stable30']

    name: npm-audit-fix-${{ matrix.branches }}

    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false
          ref: ${{ matrix.branches }}
        continue-on-error: true

      - name: Read package.json node and npm engines version
        uses: skjnldsv/read-package-engines-version-actions@06d6baf7d8f41934ab630e97d9e6c0bc9c9ac5e4 # v3
        id: versions
        with:
          fallbackNode: '^20'
          fallbackNpm: '^10'

      - name: Set up node ${{ steps.versions.outputs.nodeVersion }}
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: ${{ steps.versions.outputs.nodeVersion }}

      - name: Set up npm ${{ steps.versions.outputs.npmVersion }}
        run: npm i -g 'npm@${{ steps.versions.outputs.npmVersion }}'

      - name: Fix npm audit
        id: npm-audit
        uses: nextcloud-libraries/npm-audit-action@1b1728b2b4a7a78d69de65608efcf4db0e3e42d0 # v0.2.0

      - name: Run npm ci and npm run build
        if: steps.checkout.outcome == 'success'
        env:
          CYPRESS_INSTALL_BINARY: 0
        run: |
          npm ci
          npm run build --if-present

      - name: Create Pull Request
        if: steps.checkout.outcome == 'success'
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          token: ${{ secrets.COMMAND_BOT_PAT }}
          commit-message: 'fix(deps): Fix npm audit'
          committer: GitHub <<EMAIL>>
          author: nextcloud-command <<EMAIL>>
          signoff: true
          branch: automated/noid/${{ matrix.branches }}-fix-npm-audit
          title: '[${{ matrix.branches }}] Fix npm audit'
          body: ${{ steps.npm-audit.outputs.markdown }}
          labels: |
            dependencies
            3. to review
