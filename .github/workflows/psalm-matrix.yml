# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2022-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Static analysis

on: pull_request

concurrency:
  group: psalm-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  matrix:
    runs-on: ubuntu-latest-low
    outputs:
      ocp-matrix: ${{ steps.versions.outputs.ocp-matrix }}
    steps:
      - name: Checkout app
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Get version matrix
        id: versions
        uses: icewind1991/nextcloud-version-matrix@58becf3b4bb6dc6cef677b15e2fd8e7d48c0908f # v1.3.1

      - name: Check enforcement of minimum PHP version ${{ steps.versions.outputs.php-min }} in psalm.xml
        run: grep 'phpVersion="${{ steps.versions.outputs.php-min }}' psalm.xml

  static-analysis:
    runs-on: ubuntu-latest
    needs: matrix
    strategy:
      # do not stop on another job's failure
      fail-fast: false
      matrix: ${{ fromJson(needs.matrix.outputs.ocp-matrix) }}

    name: static-psalm-analysis ${{ matrix.ocp-version }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Set up php${{ matrix.php-min }}
        uses: shivammathur/setup-php@cf4cade2721270509d5b1c766ab3549210a39a2a # v2.33.0
        with:
          php-version: ${{ matrix.php-min }}
          extensions: bz2, ctype, curl, dom, fileinfo, gd, iconv, intl, json, libxml, mbstring, openssl, pcntl, posix, session, simplexml, xmlreader, xmlwriter, zip, zlib, sqlite, pdo_sqlite
          coverage: none
          ini-file: development
          # Temporary workaround for missing pcntl_* in PHP 8.3
          ini-values: disable_functions=
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Install dependencies
        run: |
          composer remove nextcloud/ocp --dev
          composer i


      - name: Install dependencies # zizmor: ignore[template-injection]
        run: composer require --dev 'nextcloud/ocp:${{ matrix.ocp-version }}' --ignore-platform-reqs --with-dependencies

      - name: Run coding standards check
        run: composer run psalm -- --threads=1 --monochrome --no-progress --output-format=github

  summary:
    runs-on: ubuntu-latest-low
    needs: static-analysis

    if: always()

    name: static-psalm-analysis-summary

    steps:
      - name: Summary status
        run: if ${{ needs.static-analysis.result != 'success' }}; then exit 1; fi
