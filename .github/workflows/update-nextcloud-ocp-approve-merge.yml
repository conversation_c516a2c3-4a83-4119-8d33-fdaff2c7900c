# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2023-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Auto approve nextcloud/ocp

on:
  pull_request_target:  # zizmor: ignore[dangerous-triggers]
    branches:
      - main
      - master
      - stable*

permissions:
  contents: read

concurrency:
  group: update-nextcloud-ocp-approve-merge-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  auto-approve-merge:
    if: github.actor == 'nextcloud-command'
    runs-on: ubuntu-latest-low
    permissions:
      # for hmarr/auto-approve-action to approve PRs
      pull-requests: write
      # for alexwilson/enable-github-automerge-action to approve PRs
      contents: write

    steps:
      - name: Disabled on forks
        if: ${{ github.event.pull_request.head.repo.full_name != github.repository }}
        run: |
          echo 'Can not approve PRs from forks'
          exit 1

      - uses: mdecoleman/pr-branch-name@55795d86b4566d300d237883103f052125cc7508 # v3.0.0
        id: branchname
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      # GitHub actions bot approve
      - uses: hmarr/auto-approve-action@b40d6c9ed2fa10c9a2749eca7eb004418a705501 # v2
        if: startsWith(steps.branchname.outputs.branch, 'automated/noid/') && endsWith(steps.branchname.outputs.branch, 'update-nextcloud-ocp')
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}

      # Enable GitHub auto merge
      - name: Auto merge
        uses: alexwilson/enable-github-automerge-action@56e3117d1ae1540309dc8f7a9f2825bc3c5f06ff # v2.0.0
        if: startsWith(steps.branchname.outputs.branch, 'automated/noid/') && endsWith(steps.branchname.outputs.branch, 'update-nextcloud-ocp')
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
