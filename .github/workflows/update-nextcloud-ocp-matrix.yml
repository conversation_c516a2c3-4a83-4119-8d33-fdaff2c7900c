# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2022-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Update nextcloud/ocp

on:
  workflow_dispatch:
  schedule:
    - cron: '5 2 * * 0'

permissions:
  contents: read

jobs:
  update-nextcloud-ocp:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        branches: ['master']
        target: ['stable30']

    name: update-nextcloud-ocp-${{ matrix.branches }}

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false
          ref: ${{ matrix.branches }}
          submodules: true

      - name: Set up php8.2
        uses: shivammathur/setup-php@cf4cade2721270509d5b1c766ab3549210a39a2a # v2.33.0
        with:
          php-version: 8.2
          # https://docs.nextcloud.com/server/stable/admin_manual/installation/source_installation.html#prerequisites-for-manual-installation
          extensions: bz2, ctype, curl, dom, fileinfo, gd, iconv, intl, json, libxml, mbstring, openssl, pcntl, posix, session, simplexml, xmlreader, xmlwriter, zip, zlib, sqlite, pdo_sqlite
          coverage: none
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Read codeowners
        id: codeowners
        run: |
          grep '/appinfo/info.xml' .github/CODEOWNERS | cut -f 2- -d ' ' | xargs | awk '{ print "codeowners="$0 }' >> $GITHUB_OUTPUT
        continue-on-error: true

      - name: Composer install
        run: composer install

      - name: Composer update nextcloud/ocp
        id: update_branch
        run: composer require --dev nextcloud/ocp:dev-${{ matrix.target }}

      - name: Raise on issue on failure
        uses: dacbd/create-issue-action@cdb57ab6ff8862aa09fee2be6ba77a59581921c2 # v2.0.0
        if: ${{ failure() && steps.update_branch.conclusion == 'failure' }}
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          title: 'Failed to update nextcloud/ocp package'
          body: 'Please check the output of the GitHub action and manually resolve the issues<br>${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}<br>${{ steps.codeowners.outputs.codeowners }}'

      - name: Reset checkout 3rdparty
        run: |
          git clean -f 3rdparty
          git checkout 3rdparty
        continue-on-error: true

      - name: Reset checkout vendor
        run: |
          git clean -f vendor
          git checkout vendor
        continue-on-error: true

      - name: Reset checkout vendor-bin
        run: |
          git clean -f vendor-bin
          git checkout vendor-bin
        continue-on-error: true

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          token: ${{ secrets.COMMAND_BOT_PAT }}
          commit-message: 'chore(dev-deps): Bump nextcloud/ocp package'
          committer: GitHub <<EMAIL>>
          author: nextcloud-command <<EMAIL>>
          signoff: true
          branch: 'automated/noid/${{ matrix.branches }}-update-nextcloud-ocp'
          title: '[${{ matrix.branches }}] Update nextcloud/ocp dependency'
          body: |
            Auto-generated update of [nextcloud/ocp](https://github.com/nextcloud-deps/ocp/) dependency
          labels: |
            dependencies
            3. to review
