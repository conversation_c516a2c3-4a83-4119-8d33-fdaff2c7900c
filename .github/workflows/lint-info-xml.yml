# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2021-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Lint info.xml

on: pull_request

permissions:
  contents: read

concurrency:
  group: lint-info-xml-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  xml-linters:
    runs-on: ubuntu-latest-low

    name: info.xml lint
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Download schema
        run: wget https://raw.githubusercontent.com/nextcloud/appstore/master/nextcloudappstore/api/v1/release/info.xsd

      - name: Lint info.xml
        uses: Christoph<PERSON><PERSON><PERSON>/xmllint-action@36f2a302f84f8c83fceea0b9c59e1eb4a616d3c1 # v1.2
        with:
          xml-file: ./appinfo/info.xml
          xml-schema-file: ./info.xsd
