# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2021-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: <PERSON>t stylelint

on: pull_request

permissions:
  contents: read

concurrency:
  group: lint-stylelint-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  lint:
    runs-on: ubuntu-latest

    name: stylelint

    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Read package.json node and npm engines version
        uses: skjnldsv/read-package-engines-version-actions@06d6baf7d8f41934ab630e97d9e6c0bc9c9ac5e4 # v3
        id: versions
        with:
          fallbackNode: '^20'
          fallbackNpm: '^10'

      - name: Set up node ${{ steps.versions.outputs.nodeVersion }}
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: ${{ steps.versions.outputs.nodeVersion }}

      - name: Set up npm ${{ steps.versions.outputs.npmVersion }}
        run: npm i -g 'npm@${{ steps.versions.outputs.npmVersion }}'

      - name: Install dependencies
        env:
          CYPRESS_INSTALL_BINARY: 0
        run: npm ci

      - name: Lint
        run: npm run stylelint
