# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2021-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Lint eslint

on: pull_request

permissions:
  contents: read

concurrency:
  group: lint-eslint-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  changes:
    runs-on: ubuntu-latest-low
    permissions:
      contents: read
      pull-requests: read

    outputs:
      src: ${{ steps.changes.outputs.src}}

    steps:
      - uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36 # v3.0.2
        id: changes
        continue-on-error: true
        with:
          filters: |
            src:
              - '.github/workflows/**'
              - 'src/**'
              - 'appinfo/info.xml'
              - 'package.json'
              - 'package-lock.json'
              - 'tsconfig.json'
              - '.eslintrc.*'
              - '.eslintignore'
              - '**.js'
              - '**.ts'
              - '**.vue'

  lint:
    runs-on: ubuntu-latest

    needs: changes
    if: needs.changes.outputs.src != 'false'

    name: NPM lint

    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Read package.json node and npm engines version
        uses: skjnldsv/read-package-engines-version-actions@06d6baf7d8f41934ab630e97d9e6c0bc9c9ac5e4 # v3
        id: versions
        with:
          fallbackNode: '^20'
          fallbackNpm: '^10'

      - name: Set up node ${{ steps.versions.outputs.nodeVersion }}
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: ${{ steps.versions.outputs.nodeVersion }}

      - name: Set up npm ${{ steps.versions.outputs.npmVersion }}
        run: npm i -g 'npm@${{ steps.versions.outputs.npmVersion }}'

      - name: Install dependencies
        env:
          CYPRESS_INSTALL_BINARY: 0
          PUPPETEER_SKIP_DOWNLOAD: true
        run: npm ci

      - name: Lint
        run: npm run lint

  summary:
    permissions:
      contents: none
    runs-on: ubuntu-latest-low
    needs: [changes, lint]

    if: always()

    # This is the summary, we just avoid to rename it so that branch protection rules still match
    name: eslint

    steps:
      - name: Summary status
        run: if ${{ needs.changes.outputs.src != 'false' && needs.lint.result != 'success' }}; then exit 1; fi
