# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2021-2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Lint php

on: pull_request

permissions:
  contents: read

concurrency:
  group: lint-php-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  matrix:
    runs-on: ubuntu-latest-low
    outputs:
      php-versions: ${{ steps.versions.outputs.php-versions }}
    steps:
      - name: Checkout app
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Get version matrix
        id: versions
        uses: icewind1991/nextcloud-version-matrix@58becf3b4bb6dc6cef677b15e2fd8e7d48c0908f # v1.0.0

  php-lint:
    runs-on: ubuntu-latest
    needs: matrix
    strategy:
      matrix:
        php-versions: ${{fromJson(needs.matrix.outputs.php-versions)}}

    name: php-lint

    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Set up php ${{ matrix.php-versions }}
        uses: shivammathur/setup-php@cf4cade2721270509d5b1c766ab3549210a39a2a # v2.33.0
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: bz2, ctype, curl, dom, fileinfo, gd, iconv, intl, json, libxml, mbstring, openssl, pcntl, posix, session, simplexml, xmlreader, xmlwriter, zip, zlib, sqlite, pdo_sqlite
          coverage: none
          ini-file: development
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Lint
        run: composer run lint

  summary:
    permissions:
      contents: none
    runs-on: ubuntu-latest-low
    needs: php-lint

    if: always()

    name: php-lint-summary

    steps:
      - name: Summary status
        run: if ${{ needs.php-lint.result != 'success' && needs.php-lint.result != 'skipped' }}; then exit 1; fi
