# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-License-Identifier: MIT

name: Block unconventional commits

on:
  pull_request:
    types: [opened, ready_for_review, reopened, synchronize]

permissions:
  contents: read

concurrency:
  group: block-unconventional-commits-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  block-unconventional-commits:
    name: Block unconventional commits

    runs-on: ubuntu-latest-low

    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - uses: webiny/action-conventional-commits@8bc41ff4e7d423d56fa4905f6ff79209a78776c7 # v1.3.0
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
