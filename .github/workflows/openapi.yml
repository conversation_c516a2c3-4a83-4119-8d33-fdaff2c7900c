# This workflow is provided via the organization template repository
#
# https://github.com/nextcloud/.github
# https://docs.github.com/en/actions/learn-github-actions/sharing-workflows-with-your-organization
#
# SPDX-FileCopyrightText: 2024 Nextcloud GmbH and Nextcloud contributors
# SPDX-FileCopyrightText: 2024 <PERSON> <<EMAIL>>
# SPDX-License-Identifier: MIT

name: OpenAPI

on: pull_request

permissions:
  contents: read

concurrency:
  group: openapi-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  openapi:
    runs-on: ubuntu-latest

    if: ${{ github.repository_owner != 'nextcloud-gmbh' }}

    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Get php version
        id: php_versions
        uses: icewind1991/nextcloud-version-matrix@58becf3b4bb6dc6cef677b15e2fd8e7d48c0908f # v1.3.1

      - name: Set up php
        uses: shivammathur/setup-php@cf4cade2721270509d5b1c766ab3549210a39a2a # v2.33.0
        with:
          php-version: ${{ steps.php_versions.outputs.php-available }}
          extensions: xml
          coverage: none
          ini-file: development
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Check Typescript OpenApi types
        id: check_typescript_openapi
        uses: andstor/file-existence-action@076e0072799f4942c8bc574a82233e1e4d13e9d6 # v3.0.0
        with:
          files: "src/types/openapi/openapi*.ts"

      - name: Read package.json node and npm engines version
        if: steps.check_typescript_openapi.outputs.files_exists == 'true'
        uses: skjnldsv/read-package-engines-version-actions@06d6baf7d8f41934ab630e97d9e6c0bc9c9ac5e4 # v3
        id: node_versions
        # Continue if no package.json
        continue-on-error: true
        with:
          fallbackNode: '^20'
          fallbackNpm: '^10'

      - name: Set up node ${{ steps.node_versions.outputs.nodeVersion }}
        if: ${{ steps.node_versions.outputs.nodeVersion }}
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: ${{ steps.node_versions.outputs.nodeVersion }}

      - name: Set up npm ${{ steps.node_versions.outputs.npmVersion }}
        if: ${{ steps.node_versions.outputs.nodeVersion }}
        run: npm i -g 'npm@${{ steps.node_versions.outputs.npmVersion }}'

      - name: Install dependencies
        if: ${{ steps.node_versions.outputs.nodeVersion }}
        env:
          CYPRESS_INSTALL_BINARY: 0
          PUPPETEER_SKIP_DOWNLOAD: true
        run: |
          npm ci

      - name: Set up dependencies
        run: composer i

      - name: Regenerate OpenAPI
        run: composer run openapi

      - name: Check openapi*.json and typescript changes
        run: |
          bash -c "[[ ! \"`git status --porcelain `\" ]] || (echo 'Please run \"composer run openapi\" and commit the openapi*.json files and (if applicable) src/types/openapi/openapi*.ts, see the section \"Show changes on failure\" for details' && exit 1)"

      - name: Show changes on failure
        if: failure()
        run: |
          git status
          git --no-pager diff
          exit 1 # make it red to grab attention
